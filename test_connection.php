<?php
echo "=== Test de connexion à la base de données ===\n";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=pole_industrie;charset=utf8', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connexion réussie à la base de données\n";
    
    // Vérifier les tables
    $result = $pdo->query('SHOW TABLES');
    $tables = $result->fetchAll(PDO::FETCH_COLUMN);
    echo "📋 Tables trouvées: " . implode(', ', $tables) . "\n";
    
    // Vérifier la table actualites
    if (in_array('actualites', $tables)) {
        $count = $pdo->query('SELECT COUNT(*) FROM actualites')->fetchColumn();
        echo "📰 Nombre d'actualités: " . $count . "\n";
        
        // Tester une insertion
        echo "\n=== Test d'insertion ===\n";
        $stmt = $pdo->prepare("INSERT INTO actualites (titre, contenu, categorie, statut, created_by, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
        $result = $stmt->execute(['Test insertion', 'Contenu de test', 'general', 'brouillon', 1]);
        
        if ($result) {
            $id = $pdo->lastInsertId();
            echo "✅ Insertion réussie, ID: " . $id . "\n";
            
            // Supprimer le test
            $pdo->prepare("DELETE FROM actualites WHERE id = ?")->execute([$id]);
            echo "🗑️ Test supprimé\n";
        } else {
            echo "❌ Échec de l'insertion\n";
        }
    } else {
        echo "❌ Table 'actualites' non trouvée\n";
    }
    
    // Vérifier les utilisateurs admin
    if (in_array('admin_users', $tables)) {
        $count = $pdo->query('SELECT COUNT(*) FROM admin_users')->fetchColumn();
        echo "👤 Nombre d'utilisateurs admin: " . $count . "\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    
    // Si la base n'existe pas, essayer de la créer
    if ($e->getCode() == 1049) {
        echo "🔧 Tentative de création de la base de données...\n";
        try {
            $pdo = new PDO('mysql:host=localhost;charset=utf8', 'root', '');
            $pdo->exec("CREATE DATABASE IF NOT EXISTS pole_industrie CHARACTER SET utf8 COLLATE utf8_general_ci");
            echo "✅ Base de données créée\n";
            echo "⚠️ Vous devez maintenant installer les tables via admin/install.php\n";
        } catch (PDOException $e2) {
            echo "❌ Impossible de créer la base: " . $e2->getMessage() . "\n";
        }
    }
}

echo "\n=== Test terminé ===\n";
?>
