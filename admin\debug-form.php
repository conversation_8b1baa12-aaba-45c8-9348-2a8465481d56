<?php
require_once 'includes/auth.php';
require_once '../includes/config.php';

// Vérifier l'authentification
requireLogin();

$current_user = getCurrentUser();
$message = '';
$error = '';

try {
    $pdo = getDBConnection();
    
    // Test d'insertion simple
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_insert'])) {
        $test_title = trim($_POST['test_title'] ?? '');
        $test_content = trim($_POST['test_content'] ?? '');
        
        if (!empty($test_title) && !empty($test_content)) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO actualites (titre, contenu, categorie, statut, created_by, created_at)
                    VALUES (?, ?, 'general', 'brouillon', ?, NOW())
                ");
                $result = $stmt->execute([$test_title, $test_content, $current_user['id']]);
                
                if ($result) {
                    $message = "✅ Test réussi ! Données insérées avec succès. ID: " . $pdo->lastInsertId();
                } else {
                    $error = "❌ Échec de l'insertion";
                }
            } catch (PDOException $e) {
                $error = "❌ Erreur SQL : " . $e->getMessage();
            }
        } else {
            $error = "❌ Veuillez remplir tous les champs";
        }
    }
    
    // Récupérer les dernières actualités pour vérification
    $stmt = $pdo->query("SELECT * FROM actualites ORDER BY created_at DESC LIMIT 5");
    $recent_actualites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error = "❌ Erreur de connexion : " . $e->getMessage();
}

include 'includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>🔧 Debug - Test de formulaire</h1>
        <p>Test de fonctionnement des formulaires et de la base de données</p>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error">
        <i class="fas fa-exclamation-triangle"></i>
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<div class="form-container">
    <h3>Test d'insertion</h3>
    <form method="POST">
        <div class="form-group">
            <label for="test_title">Titre de test *</label>
            <input type="text" id="test_title" name="test_title" required
                   placeholder="Entrez un titre de test">
        </div>
        
        <div class="form-group">
            <label for="test_content">Contenu de test *</label>
            <textarea id="test_content" name="test_content" rows="4" required
                      placeholder="Entrez du contenu de test"></textarea>
        </div>
        
        <button type="submit" name="test_insert" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            Tester l'insertion
        </button>
    </form>
</div>

<div class="data-table" style="margin-top: 2rem;">
    <h3>Dernières actualités (vérification)</h3>
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Titre</th>
                <th>Contenu</th>
                <th>Date création</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($recent_actualites)): ?>
                <tr>
                    <td colspan="4" class="no-data">
                        <p>Aucune actualité trouvée</p>
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($recent_actualites as $actualite): ?>
                    <tr>
                        <td><?php echo $actualite['id']; ?></td>
                        <td><?php echo htmlspecialchars($actualite['titre']); ?></td>
                        <td><?php echo htmlspecialchars(substr($actualite['contenu'], 0, 50)) . '...'; ?></td>
                        <td><?php echo $actualite['created_at']; ?></td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<div style="margin-top: 2rem;">
    <h3>Informations de debug</h3>
    <ul>
        <li><strong>Utilisateur connecté :</strong> <?php echo htmlspecialchars($current_user['username']); ?> (ID: <?php echo $current_user['id']; ?>)</li>
        <li><strong>Méthode de requête :</strong> <?php echo $_SERVER['REQUEST_METHOD']; ?></li>
        <li><strong>POST data :</strong> <?php echo !empty($_POST) ? 'Présent' : 'Absent'; ?></li>
        <?php if (!empty($_POST)): ?>
            <li><strong>Données POST :</strong> <pre><?php print_r($_POST); ?></pre></li>
        <?php endif; ?>
    </ul>
</div>

<?php include 'includes/footer.php'; ?>
