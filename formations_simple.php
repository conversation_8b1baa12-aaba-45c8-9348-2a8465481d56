<?php
$page_title = "Nos Formations - Test Simple";
$page_description = "Test simple des formations";

// Inclusion de la configuration
require_once 'includes/config.php';

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>$page_title</title>
    <link rel='stylesheet' href='css/style.css'>
</head>
<body>
    <div style='padding: 20px;'>
        <h1>Test Simple - Formations</h1>";

// Test de connexion et récupération
try {
    echo "<p style='color: green;'>✅ Inclusion config.php réussie</p>";
    
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✅ Connexion base de données réussie</p>";
    
    // Test requête simple
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM formations");
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    echo "<p>Total formations dans la table : <strong>$total</strong></p>";
    
    // Test requête avec WHERE
    $stmt = $pdo->query("SELECT COUNT(*) as actives FROM formations WHERE statut = 'active'");
    $actives = $stmt->fetch(PDO::FETCH_ASSOC)['actives'];
    echo "<p>Formations actives : <strong>$actives</strong></p>";
    
    // Récupération des formations
    $stmt = $pdo->query("SELECT * FROM formations WHERE statut = 'active' ORDER BY secteur, niveau");
    $formations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Formations récupérées : <strong>" . count($formations) . "</strong></p>";
    
    if (!empty($formations)) {
        echo "<h2>Liste des formations :</h2>";
        echo "<div style='display: grid; gap: 20px;'>";
        
        foreach ($formations as $formation) {
            echo "<div style='border: 1px solid #ddd; padding: 15px; border-radius: 8px; background: white;'>";
            echo "<h3 style='color: #1e3a8a; margin: 0 0 10px;'>" . htmlspecialchars($formation['nom']) . "</h3>";
            echo "<p><strong>Secteur :</strong> " . htmlspecialchars($formation['secteur']) . "</p>";
            echo "<p><strong>Niveau :</strong> " . htmlspecialchars($formation['niveau']) . "</p>";
            echo "<p><strong>Durée :</strong> " . htmlspecialchars($formation['duree']) . "</p>";
            echo "<p><strong>Lieu :</strong> " . htmlspecialchars($formation['lieu']) . "</p>";
            if ($formation['prix'] > 0) {
                echo "<p><strong>Prix :</strong> " . number_format($formation['prix'], 0, ',', ' ') . "€</p>";
            } else {
                echo "<p><strong>Prix :</strong> Formation gratuite</p>";
            }
            echo "<p><strong>Places :</strong> " . $formation['places_disponibles'] . " places</p>";
            echo "<p><strong>Description :</strong> " . htmlspecialchars(substr($formation['description'], 0, 200)) . "...</p>";
            echo "<a href='formation-detail.php?id=" . $formation['id'] . "' style='background: #1e3a8a; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Plus d'infos</a>";
            echo "</div>";
        }
        
        echo "</div>";
        
        echo "<div style='margin-top: 30px; padding: 15px; background: #e6ffe6; border-radius: 8px;'>";
        echo "<h3 style='color: green; margin: 0 0 10px;'>✅ Test réussi !</h3>";
        echo "<p>Les formations sont correctement récupérées de la base de données.</p>";
        echo "<p><a href='formations.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Tester formations.php</a></p>";
        echo "</div>";
        
    } else {
        echo "<div style='margin-top: 20px; padding: 15px; background: #ffe6e6; border-radius: 8px;'>";
        echo "<h3 style='color: red; margin: 0 0 10px;'>❌ Aucune formation trouvée</h3>";
        echo "<p>La requête ne retourne aucun résultat.</p>";
        echo "<p>Vérifiez que les données sont bien insérées avec le statut 'active'.</p>";
        echo "<p><a href='force_insert_data.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Réinsérer les données</a></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='margin-top: 20px; padding: 15px; background: #ffe6e6; border-radius: 8px;'>";
    echo "<h3 style='color: red; margin: 0 0 10px;'>❌ Erreur</h3>";
    echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Vérifiez que :</p>";
    echo "<ul>";
    echo "<li>MySQL est démarré</li>";
    echo "<li>La base de données 'pole_industrie' existe</li>";
    echo "<li>Les tables sont créées</li>";
    echo "<li>Le fichier includes/config.php est correct</li>";
    echo "</ul>";
    echo "</div>";
}

echo "    </div>
</body>
</html>";
?>
