<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>Administration <?php echo SITE_NAME; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="admin-body">
    
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-industry"></i>
                    <span><?php echo SITE_NAME; ?></span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Principal</div>
                    <a href="index.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        Tableau de bord
                    </a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Contenu</div>
                    <a href="actualites.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'actualites.php' ? 'active' : ''; ?>">
                        <i class="fas fa-newspaper"></i>
                        Actualités
                    </a>
                    <a href="formations.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'formations.php' ? 'active' : ''; ?>">
                        <i class="fas fa-graduation-cap"></i>
                        Formations
                    </a>
                    <a href="evenements.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'evenements.php' ? 'active' : ''; ?>">
                        <i class="fas fa-calendar"></i>
                        Événements
                    </a>
                    <a href="emplois-temps.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'emplois-temps.php' ? 'active' : ''; ?>">
                        <i class="fas fa-clock"></i>
                        Emplois du temps
                    </a>
                </div>
                
                <?php if (isAdmin()): ?>
                <div class="nav-section">
                    <div class="nav-section-title">Administration</div>
                    <a href="utilisateurs.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'utilisateurs.php' ? 'active' : ''; ?>">
                        <i class="fas fa-users"></i>
                        Utilisateurs
                    </a>
                    <a href="secteurs.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'secteurs.php' ? 'active' : ''; ?>">
                        <i class="fas fa-cogs"></i>
                        Secteurs
                    </a>
                    <a href="parametres.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'parametres.php' ? 'active' : ''; ?>">
                        <i class="fas fa-sliders-h"></i>
                        Paramètres
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Debug</div>
                    <a href="check-database.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'check-database.php' ? 'active' : ''; ?>">
                        <i class="fas fa-database"></i>
                        Vérifier BDD
                    </a>
                    <a href="debug-form.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'debug-form.php' ? 'active' : ''; ?>">
                        <i class="fas fa-bug"></i>
                        Test formulaire
                    </a>
                </div>
                <?php endif; ?>
                
                <div class="nav-section">
                    <div class="nav-section-title">Site</div>
                    <a href="../index.php" class="nav-item" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                        Voir le site
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <div class="admin-title">
                    <h1><?php echo isset($page_title) ? $page_title : 'Administration'; ?></h1>
                </div>
                
                <div class="admin-user">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars(getDisplayName()); ?></div>
                        <div class="user-role"><?php echo formatRole($_SESSION['admin_role'] ?? ''); ?></div>
                    </div>
                    <a href="logout.php" class="btn-logout">
                        <i class="fas fa-sign-out-alt"></i>
                        Se déconnecter
                    </a>
                </div>
            </header>
            
            <!-- Content -->
            <div class="admin-content">
