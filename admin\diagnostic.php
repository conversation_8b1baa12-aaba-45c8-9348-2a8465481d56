<?php
// Script de diagnostic simple
echo "<h1>Diagnostic Admin</h1>";

// 1. Vérifier PHP
echo "<h2>1. Configuration PHP</h2>";
echo "<p>Version PHP: " . phpversion() . "</p>";
echo "<p>Extension PDO: " . (extension_loaded('pdo') ? '✅ Activée' : '❌ Désactivée') . "</p>";
echo "<p>Extension PDO MySQL: " . (extension_loaded('pdo_mysql') ? '✅ Activée' : '❌ Désactivée') . "</p>";

// 2. Vérifier les fichiers
echo "<h2>2. Fichiers</h2>";
echo "<p>config.php: " . (file_exists('../includes/config.php') ? '✅ Existe' : '❌ Manquant') . "</p>";
echo "<p>auth.php: " . (file_exists('includes/auth.php') ? '✅ Existe' : '❌ Manquant') . "</p>";

// 3. Tester la connexion
echo "<h2>3. Test de connexion</h2>";
try {
    require_once '../includes/config.php';
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✅ Connexion à la base réussie</p>";
    
    // Vérifier les tables
    $tables = ['admin_users', 'actualites', 'formations'];
    foreach ($tables as $table) {
        $result = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() > 0) {
            $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            echo "<p style='color: green;'>✅ Table '$table': $count enregistrements</p>";
        } else {
            echo "<p style='color: red;'>❌ Table '$table' manquante</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur: " . $e->getMessage() . "</p>";
}

// 4. Test de session
echo "<h2>4. Test de session</h2>";
session_start();
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session active: " . (session_status() === PHP_SESSION_ACTIVE ? '✅ Oui' : '❌ Non') . "</p>";

// 5. Variables POST/GET
echo "<h2>5. Variables de requête</h2>";
echo "<p>Méthode: " . $_SERVER['REQUEST_METHOD'] . "</p>";
echo "<p>POST vide: " . (empty($_POST) ? 'Oui' : 'Non') . "</p>";
echo "<p>GET vide: " . (empty($_GET) ? 'Oui' : 'Non') . "</p>";

if (!empty($_POST)) {
    echo "<h3>Données POST:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
}

// 6. Test simple de formulaire
echo "<h2>6. Test de formulaire</h2>";
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_form'])) {
    echo "<p style='color: green;'>✅ Formulaire reçu!</p>";
    echo "<p>Données: " . htmlspecialchars($_POST['test_data'] ?? 'Aucune') . "</p>";
}

echo "<form method='POST'>
    <input type='text' name='test_data' placeholder='Données de test' required>
    <button type='submit' name='test_form'>Tester</button>
</form>";

echo "<hr>";
echo "<p><a href='login.php'>← Connexion</a> | <a href='install.php'>Installer DB</a></p>";
?>
