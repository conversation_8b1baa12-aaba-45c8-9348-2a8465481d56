<?php
// Script de réinitialisation complète et population de la base de données
require_once 'includes/config.php';

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <title>Réinitialisation Base de Données</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .step { margin: 15px 0; padding: 15px; border-radius: 8px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔄 Réinitialisation et Population de la Base de Données</h1>";

try {
    $pdo = getDBConnection();
    
    echo "<div class='step success'>✅ Connexion à la base de données réussie</div>";
    
    // 1. Supprimer et recréer les tables
    echo "<div class='step info'>🗑️ Suppression des tables existantes...</div>";
    
    $pdo->exec("DROP TABLE IF EXISTS formations");
    $pdo->exec("DROP TABLE IF EXISTS actualites");
    $pdo->exec("DROP TABLE IF EXISTS admin_users");
    
    echo "<div class='step success'>✅ Tables supprimées</div>";
    
    // 2. Créer les tables
    echo "<div class='step info'>🏗️ Création des nouvelles tables...</div>";
    
    // Table admin_users
    $pdo->exec("CREATE TABLE admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'editor') DEFAULT 'editor',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // Table formations
    $pdo->exec("CREATE TABLE formations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nom VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        duree VARCHAR(100) NOT NULL,
        niveau ENUM('CAP', 'BTS', 'Licence Pro', 'Formation Continue', 'Formation Courte', 'Apprentissage') NOT NULL,
        secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') NOT NULL,
        prix DECIMAL(10,2) DEFAULT 0.00,
        places_disponibles INT DEFAULT 0,
        date_debut DATE,
        date_fin DATE,
        lieu VARCHAR(255),
        statut ENUM('active', 'inactive', 'archive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by INT,
        FOREIGN KEY (created_by) REFERENCES admin_users(id)
    )");
    
    // Table actualites
    $pdo->exec("CREATE TABLE actualites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        titre VARCHAR(255) NOT NULL,
        contenu TEXT NOT NULL,
        extrait TEXT,
        categorie ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') NOT NULL,
        statut ENUM('brouillon', 'publie', 'archive') DEFAULT 'brouillon',
        featured BOOLEAN DEFAULT FALSE,
        date_publication DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by INT,
        FOREIGN KEY (created_by) REFERENCES admin_users(id)
    )");
    
    echo "<div class='step success'>✅ Tables créées</div>";
    
    // 3. Insérer un utilisateur admin
    echo "<div class='step info'>👤 Création de l'utilisateur admin...</div>";
    
    $stmt = $pdo->prepare("INSERT INTO admin_users (username, email, password, role) VALUES (?, ?, ?, ?)");
    $stmt->execute(['admin', '<EMAIL>', password_hash('admin123', PASSWORD_DEFAULT), 'admin']);
    
    echo "<div class='step success'>✅ Utilisateur admin créé (admin/admin123)</div>";
    
    // 4. Insérer les formations
    echo "<div class='step info'>🎓 Insertion des formations...</div>";
    
    $formations = [
        ['CAP Maintenance des Véhicules Automobiles', 'Formation complète en maintenance automobile avec pratique sur véhicules récents. Apprentissage des techniques de diagnostic électronique, mécanique moteur, systèmes de freinage, climatisation et électricité automobile. Formation en alternance avec stages en entreprise. Débouchés : mécanicien automobile, technicien diagnostic, conseiller technique.', '2 ans', 'CAP', 'automobile', 0.00, 20, '2024-09-01', '2026-06-30', 'Atelier Automobile - Bâtiment A'],
        
        ['BTS Électrotechnique et Systèmes Industriels', 'Formation en systèmes électriques industriels, domotique et énergies renouvelables. Étude des automatismes, variateurs de vitesse, réseaux industriels, supervision SCADA. Programmation automates Siemens et Schneider. Stage de 10 semaines en entreprise. Débouchés : technicien électrotechnique, automaticien, technicien maintenance.', '2 ans', 'BTS', 'electricite', 0.00, 15, '2024-09-01', '2026-06-30', 'Laboratoire Électricité - Bâtiment B'],
        
        ['Formation Continue Soudage TIG/MIG/MAG', 'Perfectionnement en techniques de soudage industriel sur tous matériaux : acier, inox, aluminium. Apprentissage des procédés TIG, MIG/MAG, soudage orbital. Certification selon normes EN ISO 9606. Formation très demandée dans l\'industrie. Matériel Lincoln Electric et Fronius. Débouchés : soudeur qualifié, contrôleur soudage.', '3 mois', 'Formation Continue', 'mecanique', 2500.00, 12, '2024-03-01', '2024-05-31', 'Atelier Soudage - Bâtiment C'],
        
        ['Licence Pro Génie Thermique et Énergies', 'Spécialisation en systèmes de chauffage, climatisation, énergies renouvelables et efficacité énergétique. Étude des pompes à chaleur, systèmes solaires, géothermie. Dimensionnement d\'installations, audit énergétique. Stage de 16 semaines. Débouchés : technicien bureau d\'études, chargé d\'affaires énergies renouvelables.', '1 an', 'Licence Pro', 'thermique', 0.00, 25, '2024-09-01', '2025-06-30', 'Campus Principal - Bâtiment D'],
        
        ['Formation Courte Hydraulique Industrielle', 'Initiation complète aux systèmes hydrauliques industriels et mobiles. Étude des composants : pompes, vérins, distributeurs, accumulateurs. Lecture de schémas hydrauliques, maintenance préventive. Formation très pratique avec bancs d\'essais. Débouchés : technicien hydraulique, mainteneur équipements mobiles.', '1 mois', 'Formation Courte', 'mecanique', 1200.00, 10, '2024-02-01', '2024-02-29', 'Atelier Hydraulique - Bâtiment E'],
        
        ['Apprentissage Construction Navale', 'Formation complète en alternance aux métiers de la construction navale : chaudronnerie, soudage, assemblage de coques. Lecture de plans navals, travail sur vrais navires. Partenariat avec les Chantiers de l\'Atlantique. Formation d\'excellence très demandée. Emploi garanti à l\'issue de la formation.', '3 ans', 'Apprentissage', 'navale', 0.00, 18, '2024-09-01', '2027-06-30', 'Chantier Naval - Saint-Nazaire']
    ];
    
    $formations_count = 0;
    foreach ($formations as $formation) {
        $stmt = $pdo->prepare("INSERT INTO formations (nom, description, duree, niveau, secteur, prix, places_disponibles, date_debut, date_fin, lieu, statut, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', 1)");
        $stmt->execute($formation);
        $formations_count++;
    }
    
    echo "<div class='step success'>✅ $formations_count formations insérées</div>";
    
    // 5. Insérer les actualités
    echo "<div class='step info'>📰 Insertion des actualités...</div>";
    
    $actualites = [
        ['Nouvelle Formation en Robotique Industrielle 4.0', 'Le Pôle Industrie lance une nouvelle formation révolutionnaire dédiée à la robotique industrielle 4.0. Cette formation de 6 mois permettra aux participants de maîtriser les technologies robotiques les plus avancées, de la programmation à la maintenance en passant par l\'intelligence artificielle.\n\nLes étudiants travailleront sur des robots collaboratifs dernière génération (Universal Robots, KUKA, ABB) et découvriront les enjeux de l\'industrie 4.0 : IoT, big data, maintenance prédictive.\n\nPartenariats avec Siemens, Schneider Electric et Dassault Systèmes pour des projets concrets. Stage de 2 mois en entreprise garanti. Débouchés : roboticien, intégrateur systèmes, chef de projet industrie 4.0.', 'Découvrez notre nouvelle formation en robotique industrielle 4.0, une opportunité unique de se former aux technologies de demain.', 'mecanique', 'publie', 1, '2024-01-15'],
        
        ['Partenariat Stratégique avec Renault pour l\'Électrique', 'Nous sommes fiers d\'annoncer notre nouveau partenariat stratégique avec Renault pour la formation de techniciens spécialisés dans les véhicules électriques. Ce partenariat révolutionnaire permettra à nos étudiants d\'accéder à des équipements de pointe.\n\nRenault fournit 5 véhicules électriques pour la formation (Zoé, Mégane E-Tech, Kangoo E-Tech), une station de recharge rapide et les outils de diagnostic officiels. Nos formateurs bénéficient d\'une formation certifiante chez Renault.\n\nLes étudiants auront accès à des stages prioritaires dans le réseau Renault (200 concessions en France) et des débouchés garantis. Certification Renault officielle incluse.', 'Un nouveau partenariat stratégique avec Renault pour renforcer nos formations véhicules électriques et garantir l\'emploi.', 'automobile', 'publie', 1, '2024-01-10'],
        
        ['Journée Portes Ouvertes 2024 : Un Succès Retentissant', 'Plus de 500 visiteurs ont participé à notre journée portes ouvertes le 15 mars 2024, confirmant l\'attractivité de nos formations industrielles. Au programme : visites des ateliers ultramodernes, démonstrations techniques spectaculaires, rencontres avec les formateurs experts.\n\nPoints forts : démonstration de soudage robotisé, présentation des véhicules électriques, atelier impression 3D, simulation de réalité virtuelle pour la maintenance. Les témoignages d\'anciens étudiants ont particulièrement marqué les visiteurs.\n\nRésultats : 150 dossiers de candidature récupérés, 80 rendez-vous pris pour des entretiens, 25 inscriptions immédiates. Prochaine journée portes ouvertes : 15 juin 2024.', 'Retour sur notre journée portes ouvertes qui a attiré plus de 500 visiteurs et confirmé l\'excellence de nos formations.', 'general', 'publie', 0, '2024-03-16'],
        
        ['Prix de l\'Innovation Énergétique pour nos Étudiants', 'Nos étudiants en génie thermique ont remporté le Prix de l\'Innovation Énergétique 2024 avec leur système de chauffage intelligent qui réduit la consommation énergétique de 30%. Ce projet révolutionnaire utilise l\'intelligence artificielle pour optimiser les performances thermiques.\n\nLe système développé par l\'équipe de 6 étudiants combine capteurs IoT, algorithmes d\'apprentissage automatique et interface utilisateur intuitive. Tests concluants sur le campus : 30% d\'économies d\'énergie, 25% de réduction des émissions CO2.\n\nCe prix, doté de 10 000€, récompense l\'innovation dans le domaine énergétique. L\'équipe sera reçue au Ministère de la Transition Énergétique. Deux entreprises ont manifesté leur intérêt pour commercialiser cette innovation.', 'Un projet étudiant révolutionnaire en génie thermique remporte le prestigieux Prix de l\'Innovation Énergétique 2024.', 'thermique', 'publie', 1, '2024-01-01'],
        
        ['Nouveau Laboratoire d\'Électronique Embarquée', 'Inauguration de notre nouveau laboratoire d\'électronique embarquée de 200 m², équipé des dernières technologies pour la formation aux systèmes automobiles modernes et à l\'IoT industriel.\n\nÉquipements de pointe : 20 postes de développement avec oscilloscopes numériques, analyseurs de protocoles CAN/LIN, cartes de développement STM32, Arduino, Raspberry Pi. Bancs d\'essais pour systèmes ADAS, simulateurs de réseaux véhicules.\n\nCe laboratoire, financé par la Région (500 000€), forme aux métiers d\'avenir : développeur embarqué, intégrateur systèmes, expert cybersécurité automobile. Partenariats avec Valeo, Continental, Bosch pour des projets étudiants.', 'Un nouveau laboratoire de pointe pour former aux technologies automobiles et électroniques de demain.', 'electricite', 'publie', 0, '2024-02-20']
    ];
    
    $actualites_count = 0;
    foreach ($actualites as $actualite) {
        $stmt = $pdo->prepare("INSERT INTO actualites (titre, contenu, extrait, categorie, statut, featured, date_publication, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, 1)");
        $stmt->execute($actualite);
        $actualites_count++;
    }
    
    echo "<div class='step success'>✅ $actualites_count actualités insérées</div>";
    
    // 6. Vérification finale
    echo "<div class='step info'>🔍 Vérification finale...</div>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM formations WHERE statut = 'active'");
    $total_formations = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM actualites WHERE statut = 'publie'");
    $total_actualites = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM actualites WHERE statut = 'publie' AND featured = 1");
    $actualites_featured = $stmt->fetchColumn();
    
    echo "<div class='step success'>
            <h2>🎉 Réinitialisation terminée avec succès !</h2>
            <p><strong>Formations actives :</strong> $total_formations</p>
            <p><strong>Actualités publiées :</strong> $total_actualites</p>
            <p><strong>Actualités à la une :</strong> $actualites_featured</p>
          </div>";
    
    echo "<div class='step info'>
            <h3>🚀 Testez maintenant vos pages :</h3>
            <p>
                <a href='formations.php' class='btn'>🎓 Page Formations</a>
                <a href='actualites.php' class='btn'>📰 Page Actualités</a>
                <a href='index.php' class='btn'>🏠 Accueil</a>
            </p>
            <p>
                <a href='test_formations.php' class='btn'>🧪 Test Formations</a>
                <a href='test_actualites.php' class='btn'>🧪 Test Actualités</a>
            </p>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='step error'>
            <h2>❌ Erreur lors de la réinitialisation</h2>
            <p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>
            <p>Vérifiez que MySQL est démarré et que vous avez les droits d\'administration.</p>
          </div>";
}

echo "    </div>
</body>
</html>";
?>
